#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接检测脚本 - 加载整合的YOLO+OCR模型进行检测
使用模型: models/integrated_yolo_ocr_model.pt
检测图像: DaYuanTuZ_0.png
"""

import os
import json
import time
import warnings
import cv2
import numpy as np
import torch
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.widgets import Button
import threading

# 忽略警告
warnings.filterwarnings('ignore')

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def load_and_detect():
    """
    加载模型并进行检测的主函数
    """
    print("🚀 开始YOLO+OCR检测...")
    
    # 文件路径
    model_path = "models/integrated_yolo_ocr_model.pt"
    image_path = "DaYuanTuZ_0.png"
    
    # 检查文件是否存在
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return False
        
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return False
    
    print(f"📁 模型路径: {model_path}")
    print(f"🖼️ 图像路径: {image_path}")
    
    try:
        # 方法1: 尝试直接使用YOLO进行检测
        print("\n🎯 方法1: 使用YOLO进行目标检测...")
        yolo_results = detect_with_yolo(image_path)
        
        # 方法2: 使用OCR进行文字识别
        print("\n📝 方法2: 使用OCR进行文字识别...")
        ocr_results = detect_with_ocr(image_path)
        
        # 合并结果
        final_results = {
            "image_path": image_path,
            "model_path": model_path,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "detections": yolo_results,
            "text_recognition": ocr_results
        }
        
        # 保存结果
        save_results(final_results, image_path)

        # 绘制检测结果
        print("\n🎨 绘制检测结果...")
        draw_results(image_path, final_results)

        print("\n✅ 检测完成!")
        return True
        
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def detect_with_yolo(image_path):
    """
    使用YOLO模型进行目标检测
    """
    yolo_detections = []
    
    try:
        # 导入YOLO
        from ultralytics import YOLO
        
        # 尝试加载不同的YOLO模型，优先使用训练好的模型
        yolo_model_paths = [
            "high_precision_detection/yolo11s_ocr_integrated/weights/best.pt",  # 训练好的专用模型
            "yolo11s.pt",  # 基础模型
            "yolo11n.pt",  # 轻量模型
            "yolov8n.pt"   # 备用模型
        ]
        
        yolo_model = None
        for model_path in yolo_model_paths:
            if os.path.exists(model_path):
                print(f"   📂 加载YOLO模型: {model_path}")
                yolo_model = YOLO(model_path)
                break
        
        if yolo_model is None:
            print("   ⚠️ 未找到可用的YOLO模型")
            return yolo_detections
        
        # 执行检测
        results = yolo_model.predict(
            source=image_path,
            conf=0.25,
            save=False,
            verbose=False
        )
        
        if results and len(results) > 0:
            result = results[0]
            
            if result.boxes is not None:
                boxes = result.boxes
                for i in range(len(boxes)):
                    box = boxes.xyxy[i].cpu().numpy()
                    conf = float(boxes.conf[i].cpu().numpy())
                    cls_id = int(boxes.cls[i].cpu().numpy())
                    
                    detection = {
                        "bbox": [float(box[0]), float(box[1]), float(box[2]), float(box[3])],
                        "confidence": conf,
                        "class_id": cls_id,
                        "class_name": yolo_model.names.get(cls_id, f"class_{cls_id}"),
                        "detection_type": "yolo_detection"
                    }
                    yolo_detections.append(detection)
        
        print(f"   ✓ YOLO检测完成，发现 {len(yolo_detections)} 个目标")
        
    except Exception as e:
        print(f"   ❌ YOLO检测失败: {e}")
    
    return yolo_detections

def detect_with_ocr(image_path):
    """
    使用OCR进行文字识别
    """
    ocr_results = []
    
    # 加载图像
    image = cv2.imread(image_path)
    if image is None:
        print("   ❌ 无法加载图像")
        return ocr_results
    
    # 尝试使用EasyOCR
    try:
        import easyocr
        print("   📂 使用EasyOCR引擎...")
        
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=torch.cuda.is_available())
        easyocr_results = reader.readtext(image)
        
        for (bbox, text, confidence) in easyocr_results:
            if confidence >= 0.5:
                # 转换bbox格式
                x_coords = [point[0] for point in bbox]
                y_coords = [point[1] for point in bbox]
                x1, y1 = min(x_coords), min(y_coords)
                x2, y2 = max(x_coords), max(y_coords)
                
                result = {
                    "text": text,
                    "confidence": confidence,
                    "engine": "easyocr",
                    "detection_bbox": [int(x1), int(y1), int(x2), int(y2)],
                    "detection_confidence": confidence,
                    "detection_type": "easyocr_detection",
                    "detection_class_id": -1
                }
                ocr_results.append(result)
        
        print(f"   ✓ EasyOCR识别完成，发现 {len(ocr_results)} 个文字")
        
    except ImportError:
        print("   ⚠️ EasyOCR未安装")
    except Exception as e:
        print(f"   ❌ EasyOCR识别失败: {e}")
    
    # 尝试使用CnOCR
    try:
        from cnocr import CnOcr
        print("   📂 使用CnOCR引擎...")
        
        cn_ocr = CnOcr(
            rec_model_name='densenet_lite_136-gru',
            det_model_name='db_resnet18',
            use_gpu=torch.cuda.is_available()
        )
        
        cnocr_results = cn_ocr.ocr(image)
        
        for item in cnocr_results:
            text = item.get('text', '')
            confidence = item.get('score', 0.0)
            
            if confidence >= 0.5 and text.strip():
                bbox = item.get('position', [[0, 0], [0, 0], [0, 0], [0, 0]])
                x_coords = [point[0] for point in bbox]
                y_coords = [point[1] for point in bbox]
                x1, y1 = min(x_coords), min(y_coords)
                x2, y2 = max(x_coords), max(y_coords)
                
                result = {
                    "text": text,
                    "confidence": confidence,
                    "engine": "cnocr",
                    "detection_bbox": [int(x1), int(y1), int(x2), int(y2)],
                    "detection_confidence": confidence,
                    "detection_type": "cnocr_detection",
                    "detection_class_id": -1
                }
                ocr_results.append(result)
        
        print(f"   ✓ CnOCR识别完成，总共发现 {len(ocr_results)} 个文字")
        
    except ImportError:
        print("   ⚠️ CnOCR未安装")
    except Exception as e:
        print(f"   ❌ CnOCR识别失败: {e}")
    
    return ocr_results

def save_results(results, image_path):
    """
    保存检测结果
    """
    # 创建输出目录
    output_dir = "detection_results"
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成输出文件名
    image_name = os.path.splitext(os.path.basename(image_path))[0]
    json_path = os.path.join(output_dir, f"{image_name}_detection_result.json")
    
    try:
        # 保存JSON结果
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 检测结果已保存到: {json_path}")
        
        # 打印统计信息
        print("\n📊 检测统计信息:")
        print(f"   🎯 YOLO检测数量: {len(results.get('detections', []))}")
        print(f"   📝 OCR识别数量: {len(results.get('text_recognition', []))}")
        
        # 按引擎分类统计
        text_recognition = results.get('text_recognition', [])
        if text_recognition:
            engine_stats = {}
            for item in text_recognition:
                engine = item.get('engine', 'unknown')
                engine_stats[engine] = engine_stats.get(engine, 0) + 1
            
            print("   📝 OCR引擎统计:")
            for engine, count in engine_stats.items():
                print(f"      - {engine}: {count}")
        
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")

def draw_results(image_path, results):
    """
    绘制检测结果并显示滚动窗口
    """
    try:
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print("❌ 无法加载图像用于绘制")
            return

        # 转换颜色格式 (BGR -> RGB)
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # 创建图形和子图
        fig, ax = plt.subplots(1, 1, figsize=(15, 10))

        # 显示图像
        ax.imshow(image_rgb)
        ax.set_title(f"检测结果 - {os.path.basename(image_path)}", fontsize=16, fontweight='bold')

        # 绘制YOLO检测框 (绿色)
        detections = results.get('detections', [])
        for detection in detections:
            bbox = detection['bbox']
            x1, y1, x2, y2 = bbox
            width = x2 - x1
            height = y2 - y1

            # 绘制边界框
            rect = patches.Rectangle((x1, y1), width, height,
                                   linewidth=2, edgecolor='green', facecolor='none')
            ax.add_patch(rect)

            # 添加标签
            label = f"{detection['class_name']}: {detection['confidence']:.2f}"
            ax.text(x1, y1-5, label, fontsize=10, color='green',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

        # 绘制OCR文字框 (蓝色)
        text_recognition = results.get('text_recognition', [])
        for text_item in text_recognition:
            bbox = text_item['detection_bbox']
            x1, y1, x2, y2 = bbox
            width = x2 - x1
            height = y2 - y1

            # 绘制边界框
            rect = patches.Rectangle((x1, y1), width, height,
                                   linewidth=1, edgecolor='blue', facecolor='none')
            ax.add_patch(rect)

            # 添加文字标签
            text = text_item['text']
            confidence = text_item['confidence']
            engine = text_item['engine']
            label = f"{text} ({engine}: {confidence:.2f})"
            ax.text(x1, y2+15, label, fontsize=8, color='blue',
                   bbox=dict(boxstyle="round,pad=0.2", facecolor='lightblue', alpha=0.8))

        # 设置坐标轴
        ax.set_xlim(0, image_rgb.shape[1])
        ax.set_ylim(image_rgb.shape[0], 0)  # 翻转Y轴

        # 添加图例
        legend_elements = [
            patches.Patch(color='green', label=f'YOLO检测 ({len(detections)}个)'),
            patches.Patch(color='blue', label=f'OCR识别 ({len(text_recognition)}个)')
        ]
        ax.legend(handles=legend_elements, loc='upper right')

        # 启用滚动和缩放
        ax.set_aspect('equal')

        # 保存结果图像
        output_dir = "detection_results"
        os.makedirs(output_dir, exist_ok=True)
        image_name = os.path.splitext(os.path.basename(image_path))[0]
        output_path = os.path.join(output_dir, f"{image_name}_result.jpg")

        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"📁 结果图像已保存到: {output_path}")

        # 显示交互式窗口
        print("🖼️ 显示交互式结果窗口...")
        print("   - 使用鼠标滚轮缩放")
        print("   - 拖拽移动图像")
        print("   - 关闭窗口继续")

        # 启用交互式导航
        plt.subplots_adjust(bottom=0.1)

        # 显示窗口
        plt.show()

    except Exception as e:
        print(f"❌ 绘制结果失败: {e}")
        import traceback
        traceback.print_exc()

class ScrollableWindow:
    """
    可滚动的图像显示窗口
    """
    def __init__(self, image_path, results):
        self.image_path = image_path
        self.results = results
        self.zoom_factor = 1.0
        self.pan_x = 0
        self.pan_y = 0

    def show(self):
        """显示可滚动窗口"""
        try:
            # 使用OpenCV显示窗口
            image = cv2.imread(self.image_path)
            if image is None:
                return

            # 绘制检测结果到图像上
            result_image = self.draw_on_image(image.copy())

            # 创建窗口
            window_name = f"检测结果 - {os.path.basename(self.image_path)}"
            cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
            cv2.resizeWindow(window_name, 1200, 800)

            # 设置鼠标回调
            cv2.setMouseCallback(window_name, self.mouse_callback)

            print("🖼️ OpenCV窗口控制:")
            print("   - 鼠标滚轮: 缩放")
            print("   - 拖拽: 移动")
            print("   - ESC或Q: 退出")

            while True:
                # 应用缩放和平移
                display_image = self.apply_transform(result_image)
                cv2.imshow(window_name, display_image)

                key = cv2.waitKey(1) & 0xFF
                if key == 27 or key == ord('q'):  # ESC或Q键退出
                    break

            cv2.destroyAllWindows()

        except Exception as e:
            print(f"❌ 显示滚动窗口失败: {e}")

    def draw_on_image(self, image):
        """在图像上绘制检测结果"""
        # 绘制YOLO检测框
        detections = self.results.get('detections', [])
        for detection in detections:
            bbox = detection['bbox']
            x1, y1, x2, y2 = map(int, bbox)

            # 绘制绿色框
            cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # 添加标签
            label = f"{detection['class_name']}: {detection['confidence']:.2f}"
            cv2.putText(image, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX,
                       0.6, (0, 255, 0), 2)

        # 绘制OCR文字框
        text_recognition = self.results.get('text_recognition', [])
        for text_item in text_recognition:
            bbox = text_item['detection_bbox']
            x1, y1, x2, y2 = map(int, bbox)

            # 绘制蓝色框
            cv2.rectangle(image, (x1, y1), (x2, y2), (255, 0, 0), 1)

            # 添加文字标签
            text = text_item['text']
            cv2.putText(image, text, (x1, y2+20), cv2.FONT_HERSHEY_SIMPLEX,
                       0.5, (255, 0, 0), 1)

        return image

    def mouse_callback(self, event, x, y, flags, param):
        """鼠标回调函数"""
        if event == cv2.EVENT_MOUSEWHEEL:
            # 滚轮缩放
            if flags > 0:
                self.zoom_factor *= 1.1
            else:
                self.zoom_factor *= 0.9
            self.zoom_factor = max(0.1, min(5.0, self.zoom_factor))

    def apply_transform(self, image):
        """应用缩放和平移变换"""
        if self.zoom_factor != 1.0:
            h, w = image.shape[:2]
            new_h, new_w = int(h * self.zoom_factor), int(w * self.zoom_factor)
            image = cv2.resize(image, (new_w, new_h))

        return image

def main():
    """
    主函数
    """
    print("🔧 YOLO+OCR直接检测系统")
    print("=" * 50)

    success = load_and_detect()

    if success:
        print("\n🎉 检测成功完成!")
        print("📁 请查看 detection_results/ 目录获取结果")
    else:
        print("\n💥 检测失败!")
        return 1

    return 0

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
