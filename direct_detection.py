#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接检测脚本 - 加载整合的YOLO+OCR模型进行检测
使用模型: models/integrated_yolo_ocr_model.pt
检测图像: DaYuanTuZ_0.png
"""

import os
import json
import time
import warnings
import cv2
import numpy as np
import torch
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.widgets import Button
import threading

# 忽略警告
warnings.filterwarnings('ignore')

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def load_and_detect():
    """
    加载模型并进行检测的主函数
    """
    print("🚀 开始YOLO+OCR检测...")

    # 文件路径
    model_path = "models/integrated_yolo_ocr_model.pt"
    image_path = "DaYuanTuZ_0.png"

    # 检查文件是否存在
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return False

    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return False

    print(f"📁 模型路径: {model_path}")
    print(f"🖼️ 图像路径: {image_path}")

    try:
        # 方法1: 尝试直接使用YOLO进行检测
        print("\n🎯 方法1: 使用YOLO进行目标检测...")
        yolo_results = detect_with_yolo(image_path, model_path)

        # 方法2: 使用OCR进行文字识别
        print("\n📝 方法2: 使用OCR进行文字识别...")
        ocr_results = detect_with_ocr(image_path)

        # 合并结果
        final_results = {
            "image_path": image_path,
            "model_path": model_path,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "detections": yolo_results,
            "text_recognition": ocr_results
        }

        # 保存结果
        save_results(final_results, image_path)

        # 绘制检测结果
        print("\n🎨 绘制检测结果...")
        draw_results(image_path, final_results)

        print("\n✅ 检测完成!")
        return True

    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def detect_with_yolo(image_path, model_path):
    """
    使用YOLO模型进行目标检测
    """
    yolo_detections = []

    try:
        print(f"   📂 加载整合YOLO+OCR模型: {model_path}")

        # 首先尝试加载整合模型
        try:
            # 导入训练脚本中的加载函数
            import sys
            sys.path.append('.')
            from train import load_integrated_model

            integrated_model = load_integrated_model(model_path)
            if integrated_model is not None:
                print("   ✅ 整合模型加载成功，使用YOLO组件进行检测")
                # 使用整合模型的YOLO组件
                yolo_model = integrated_model.yolo_model
            else:
                raise Exception("整合模型加载失败")

        except Exception as e:
            print(f"   ⚠️ 整合模型加载失败: {e}")
            print("   🔄 回退到直接加载YOLO模型...")

            # 回退到直接使用YOLO加载
            from ultralytics import YOLO
            yolo_model = YOLO(model_path)

        # 执行检测
        results = yolo_model.predict(
            source=image_path,
            conf=0.25,
            save=False,
            verbose=False
        )

        if results and len(results) > 0:
            result = results[0]

            if result.boxes is not None:
                boxes = result.boxes
                for i in range(len(boxes)):
                    box = boxes.xyxy[i].cpu().numpy()
                    conf = float(boxes.conf[i].cpu().numpy())
                    cls_id = int(boxes.cls[i].cpu().numpy())

                    detection = {
                        "bbox": [float(box[0]), float(box[1]), float(box[2]), float(box[3])],
                        "confidence": conf,
                        "class_id": cls_id,
                        "class_name": yolo_model.names.get(cls_id, f"class_{cls_id}"),
                        "detection_type": "yolo_detection"
                    }
                    yolo_detections.append(detection)

        print(f"   ✓ YOLO检测完成，发现 {len(yolo_detections)} 个目标")

    except Exception as e:
        print(f"   ❌ YOLO检测失败: {e}")
        import traceback
        traceback.print_exc()

    return yolo_detections

def detect_with_ocr(image_path):
    """
    使用OCR进行文字识别
    """
    ocr_results = []

    # 方法1: 使用EasyOCR
    try:
        import easyocr
        print("   📝 使用EasyOCR进行文字识别...")

        # 创建EasyOCR读取器
        reader = easyocr.Reader(['ch_sim', 'en'], model_storage_directory='easyocr_models')

        # 执行OCR识别
        results = reader.readtext(image_path)

        for result in results:
            bbox, text, confidence = result

            # 转换边界框格式
            x1 = min([point[0] for point in bbox])
            y1 = min([point[1] for point in bbox])
            x2 = max([point[0] for point in bbox])
            y2 = max([point[1] for point in bbox])

            ocr_item = {
                "bbox": [float(x1), float(y1), float(x2), float(y2)],
                "text": text,
                "confidence": float(confidence),
                "engine": "easyocr",
                "detection_type": "text_recognition"
            }
            ocr_results.append(ocr_item)

        print(f"   ✓ EasyOCR识别完成，发现 {len(results)} 个文字区域")

    except ImportError:
        print("   ⚠️ EasyOCR未安装")
    except Exception as e:
        print(f"   ❌ EasyOCR识别失败: {e}")

    # 方法2: 使用CnOCR (如果EasyOCR失败或未安装)
    if len(ocr_results) == 0:
        try:
            from cnocr import CnOcr
            print("   📝 使用CnOCR进行文字识别...")

            # 创建CnOCR实例
            ocr = CnOcr(det_model_name='ch_PP-OCRv3_det', rec_model_name='ch_PP-OCRv3_rec')

            # 执行OCR识别
            results = ocr.ocr(image_path)

            for result in results:
                bbox = result['position']
                text = result['text']
                confidence = result.get('score', 0.0)

                # 转换边界框格式
                x1 = min([point[0] for point in bbox])
                y1 = min([point[1] for point in bbox])
                x2 = max([point[0] for point in bbox])
                y2 = max([point[1] for point in bbox])

                ocr_item = {
                    "bbox": [float(x1), float(y1), float(x2), float(y2)],
                    "text": text,
                    "confidence": float(confidence),
                    "engine": "cnocr",
                    "detection_type": "text_recognition"
                }
                ocr_results.append(ocr_item)

            print(f"   ✓ CnOCR识别完成，发现 {len(results)} 个文字区域")

        except ImportError:
            print("   ⚠️ CnOCR未安装")
        except Exception as e:
            print(f"   ❌ CnOCR识别失败: {e}")

    return ocr_results

def save_results(results, image_path):
    """
    保存检测结果
    """
    # 创建输出目录
    output_dir = "detection_results"
    os.makedirs(output_dir, exist_ok=True)

    # 生成输出文件名
    image_name = os.path.splitext(os.path.basename(image_path))[0]
    json_path = os.path.join(output_dir, f"{image_name}_detection_result.json")

    try:
        # 保存JSON结果
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"\n📁 检测结果已保存到: {json_path}")

        # 打印统计信息
        print("\n📊 检测统计信息:")
        print(f"   🎯 YOLO检测数量: {len(results.get('detections', []))}")
        print(f"   📝 OCR识别数量: {len(results.get('text_recognition', []))}")

        # 按引擎分类统计
        text_recognition = results.get('text_recognition', [])
        if text_recognition:
            engine_stats = {}
            for item in text_recognition:
                engine = item.get('engine', 'unknown')
                engine_stats[engine] = engine_stats.get(engine, 0) + 1

            print("   📝 OCR引擎统计:")
            for engine, count in engine_stats.items():
                print(f"      - {engine}: {count}")

    except Exception as e:
        print(f"❌ 保存结果失败: {e}")

def draw_results(image_path, results):
    """
    绘制检测结果
    """
    try:
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return

        # 转换颜色格式 (BGR -> RGB)
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # 创建图形和子图
        fig, ax = plt.subplots(1, 1, figsize=(15, 10))

        # 显示图像
        ax.imshow(image_rgb)
        ax.set_title(f"检测结果 - {os.path.basename(image_path)}", fontsize=16, fontweight='bold')

        # 绘制YOLO检测框 (绿色)
        detections = results.get('detections', [])
        for detection in detections:
            bbox = detection['bbox']
            x1, y1, x2, y2 = bbox
            width = x2 - x1
            height = y2 - y1

            # 绘制边界框
            rect = patches.Rectangle((x1, y1), width, height,
                                   linewidth=2, edgecolor='green', facecolor='none')
            ax.add_patch(rect)

            # 添加标签
            label = f"{detection['class_name']}: {detection['confidence']:.2f}"
            ax.text(x1, y1-5, label, fontsize=10, color='green',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

        # 绘制OCR文字框 (蓝色)
        text_recognition = results.get('text_recognition', [])
        for text_item in text_recognition:
            bbox = text_item['bbox']
            x1, y1, x2, y2 = bbox
            width = x2 - x1
            height = y2 - y1

            # 绘制边界框
            rect = patches.Rectangle((x1, y1), width, height,
                                   linewidth=2, edgecolor='blue', facecolor='none')
            ax.add_patch(rect)

            # 添加文字标签
            text = text_item['text']
            confidence = text_item['confidence']
            engine = text_item.get('engine', 'ocr')
            label = f"{text} ({confidence:.2f})"
            ax.text(x1, y2+15, label, fontsize=8, color='blue',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

        # 添加图例
        legend_elements = [
            patches.Patch(color='green', label=f'YOLO检测 ({len(detections)})'),
            patches.Patch(color='blue', label=f'OCR识别 ({len(text_recognition)})')
        ]
        ax.legend(handles=legend_elements, loc='upper right')

        # 启用滚动和缩放
        ax.set_aspect('equal')

        # 保存结果图像
        output_dir = "detection_results"
        os.makedirs(output_dir, exist_ok=True)
        image_name = os.path.splitext(os.path.basename(image_path))[0]
        output_path = os.path.join(output_dir, f"{image_name}_result.jpg")

        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"📁 结果图像已保存到: {output_path}")

        # 显示交互式窗口
        print("🖼️ 显示交互式结果窗口...")
        print("   - 使用鼠标滚轮缩放")
        print("   - 拖拽移动图像")
        print("   - 关闭窗口继续")

        # 启用交互式导航
        plt.subplots_adjust(bottom=0.1)

        # 显示窗口
        plt.show()

    except Exception as e:
        print(f"❌ 绘制结果失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """
    主函数
    """
    print("🔧 YOLO+OCR直接检测系统")
    print("=" * 50)

    success = load_and_detect()

    if success:
        print("\n🎉 检测成功完成!")
        print("📁 请查看 detection_results/ 目录获取结果")
    else:
        print("\n💥 检测失败!")
        return 1

    return 0

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)